{"name": "CareMate Public API", "api_id": "caremate-public", "org_id": "default", "definition": {"location": "header", "key": "version"}, "use_keyless": true, "custom_middleware": {"driver": "otto", "post": [{"name": "CareMatePublic", "path": "./middleware/caremate-public.js", "require_session": false, "raw_body_only": false}]}, "version_data": {"not_versioned": true, "versions": {"Default": {"name": "<PERSON><PERSON><PERSON>"}}}, "proxy": {"listen_path": "/caremate/api/", "target_url": "http://************:3001/api/", "strip_listen_path": true}}